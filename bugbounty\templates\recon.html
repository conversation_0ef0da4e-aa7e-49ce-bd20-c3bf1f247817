<!DOCTYPE html>
<html lang="en">
	<head>
		<meta charset="UTF-8" />
		<meta name="viewport" content="width=device-width, initial-scale=1.0" />
		<title>Recon</title>
		<style>
			body {
				font-family: Arial, sans-serif;
				margin: 0;
				padding: 0;
			}

			h1 {
				text-align: center;
			}

			form {
				text-align: center;
				margin-top: 20px;
			}

			label {
				font-weight: bold;
			}

			input[type='text'] {
				padding: 5px;
				margin: 5px;
				border: 1px solid #ccc;
				border-radius: 3px;
			}

			#domains {
				margin-bottom: 10px;
			}

			.domainInput {
				margin-top: 5px;
			}

			button[type='button'] {
				padding: 8px 20px;
				margin: 10px;
				background-color: #007bff;
				color: #fff;
				border: none;
				border-radius: 3px;
				cursor: pointer;
			}

			button[type='button']:hover {
				background-color: #0056b3;
			}

			#reconStatus {
				margin-top: 20px;
				padding: 10px;
				border: 1px solid #ccc;
				border-radius: 3px;
			}

			.home-button {
				position: absolute;
				top: 10px;
				left: 10px;
				padding: 5px 10px;
				background-color: #007bff;
				color: #fff;
				border: none;
				border-radius: 3px;
				text-decoration: none;
			}

			.home-button:hover {
				background-color: #0056b3;
			}

			.results-button {
				position: absolute;
				top: 10px;
				right: 10px;
				padding: 5px 10px;
				background-color: #007bff;
				color: #fff;
				border: none;
				border-radius: 3px;
				text-decoration: none;
			}

			.results-button:hover {
				background-color: #0056b3;
			}
		</style>
	</head>
	<body>
		<a class="home-button" href="/">Home</a>
		<a class="results-button" href="http://127.0.0.1:8000">Results</a>
		<h1>Organization Recon</h1>
		<form id="reconForm">
			<label for="orgName">Enter Organization Name</label><br />
			<input type="text" id="orgName" name="orgName" required /><br />
			<div id="domains">
				<label for="domain">Enter Domain in Scope</label><br />
				<input type="text" class="domainInput" name="domain" required /><br />
			</div>
			<button type="button" onclick="addDomainInput()">+</button>
			<button type="button" onclick="clearDomainInputs()" hidden>-</button
			><br />
			<button type="button" onclick="startRecon()">Add to recon</button>
		</form>
		<div id="reconStatus"></div>

		<script src="https://cdnjs.cloudflare.com/ajax/libs/socket.io/4.1.3/socket.io.js"></script>
		<script>
			var socket = io();

			function addDomainInput() {
				var domainInput = document.createElement('input');
				domainInput.type = 'text';
				domainInput.className = 'domainInput';
				domainInput.name = 'domain';
				document.getElementById('domains').appendChild(domainInput);
				document.getElementsByTagName('button')[1].removeAttribute('hidden');
			}

			function clearDomainInputs() {
				var domainInputs = document.getElementsByClassName('domainInput');
				for (var i = domainInputs.length - 1; i > 0; i--) {
					domainInputs[i].parentNode.removeChild(domainInputs[i]);
				}
				document
					.getElementsByTagName('button')[1]
					.setAttribute('hidden', 'true');
			}

			function startRecon() {
				var orgName = document.getElementById('orgName').value;
				var domainInputs = document.getElementsByClassName('domainInput');
				var domains = [];
				for (var i = 0; i < domainInputs.length; i++) {
					domains.push(domainInputs[i].value);
				}
				socket.emit('start_recon', { org_name: orgName, domains: domains });
			}

			socket.on('recon_update', function (data) {
				var reconStatus = document.getElementById('reconStatus');
				reconStatus.innerHTML += '<p>' + data.data + '</p>';
			});
		</script>
	</body>
</html>
