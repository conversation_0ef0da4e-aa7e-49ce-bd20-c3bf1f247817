<!DOCTYPE html>
<html lang="en">
	<head>
		<meta charset="UTF-8" />
		<meta name="viewport" content="width=device-width, initial-scale=1.0" />
		<title>IP Lookup Tool</title>
		<style>
			table {
				width: 100%;
				border-collapse: collapse;
			}

			th,
			td {
				padding: 8px;
				border: 1px solid #ddd;
				text-align: left;
			}

			th {
				background-color: #f2f2f2;
			}

			tr:nth-child(even) {
				background-color: #f2f2f2;
			}

			tr:hover {
				background-color: #ddd;
			}
		</style>

		<style>
			body {
				font-family: Arial, sans-serif;
				margin: 0;
				padding: 0;
			}

			h1 {
				text-align: center;
			}

			form {
				text-align: center;
				margin-top: 20px;
			}

			label {
				font-weight: bold;
			}

			input[type='text'] {
				padding: 5px;
				margin: 5px;
				border: 1px solid #ccc;
				border-radius: 3px;
			}

			button[type='submit'] {
				padding: 8px 20px;
				margin: 10px;
				background-color: #007bff;
				color: #fff;
				border: none;
				border-radius: 3px;
				cursor: pointer;
			}

			button[type='submit']:hover {
				background-color: #0056b3;
			}

			#ipLookupOutput {
				margin-top: 20px;
				padding: 10px;
				border: 1px solid #ccc;
				border-radius: 3px;
				overflow-y: auto;
				max-height: auto;
			}

			.home-button {
				position: absolute;
				top: 10px;
				left: 10px;
				padding: 5px 10px;
				background-color: #007bff;
				color: #fff;
				border: none;
				border-radius: 3px;
				text-decoration: none;
			}

			.home-button:hover {
				background-color: #0056b3;
			}
		</style>
	</head>
	<body>
		<a href="/" class="home-button">Home</a>
		<h1>IP Lookup Tool</h1>
		<form id="ipLookupForm">
			<label for="ipAddress">Enter IP Address</label>
			<input type="text" id="ipAddress" name="ipAddress" required />
			<button type="submit">Start IP Lookup</button>
		</form>
		<div id="ipLookupOutput"></div>

		<script src="https://cdnjs.cloudflare.com/ajax/libs/socket.io/4.2.0/socket.io.js"></script>
		<script>
			var socket = io();

			document.getElementById('ipLookupForm').onsubmit = function (event) {
				event.preventDefault();
				var ipAddress = document.getElementById('ipAddress').value;
				document.getElementById('ipLookupOutput').innerHTML = '';
				socket.emit('start_iplookup', { ipAddress: ipAddress });
			};

			socket.on('iplookup_update', function (data) {
				var ipLookupOutput = document.getElementById('ipLookupOutput');
				var jsonData = JSON.parse(data.data);
				var tableHTML = '<table>';
				for (var key in jsonData) {
					if (jsonData.hasOwnProperty(key)) {
						tableHTML += '<tr>';
						tableHTML += '<th>' + key + '</th>';
						tableHTML += '<td>' + jsonData[key] + '</td>';
						tableHTML += '</tr>';
					}
				}
				tableHTML += '</table>';
				ipLookupOutput.innerHTML = tableHTML;
			});
		</script>
	</body>
</html>
