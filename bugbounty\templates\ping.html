<!DOCTYPE html>
<html lang="en">
	<head>
		<meta charset="UTF-8" />
		<meta name="viewport" content="width=device-width, initial-scale=1.0" />
		<title>Ping Tool</title>
		<style>
			body {
				font-family: Arial, sans-serif;
				margin: 0;
				padding: 0;
			}

			h1 {
				text-align: center;
			}

			form {
				text-align: center;
				margin-top: 20px;
			}

			label {
				font-weight: bold;
			}

			input[type='text'] {
				padding: 5px;
				margin: 5px;
				border: 1px solid #ccc;
				border-radius: 3px;
			}

			button[type='submit'] {
				padding: 8px 20px;
				margin: 10px;
				background-color: #007bff;
				color: #fff;
				border: none;
				border-radius: 3px;
				cursor: pointer;
			}

			button[type='submit']:hover {
				background-color: #0056b3;
			}

			#pingOutput {
				margin-top: 20px;
				padding: 10px;
				border: 1px solid #ccc;
				border-radius: 3px;
				overflow-y: auto;
				max-height: 300px;
			}

			#outputText {
				text-align: center;
				font-weight: bold;
			}

			#resultText {
				text-align: center;
				margin-top: 10px;
			}

			/* Styles for the home button */
			.home-button {
				position: absolute;
				top: 10px;
				left: 10px;
				padding: 5px 10px;
				background-color: #007bff;
				color: #fff;
				border: none;
				border-radius: 3px;
				text-decoration: none;
			}

			.home-button:hover {
				background-color: #0056b3;
			}
		</style>
	</head>
	<body>
		<a href="/" class="home-button">Home</a>
		<h1>Ping Tool</h1>
		<form id="pingForm">
			<label for="domain">Enter Domain</label>
			<input type="text" id="domain" name="domain" required />
			<button type="submit">Start Ping</button>
		</form>
		<div id="pingOutput"></div>

		<script src="https://cdnjs.cloudflare.com/ajax/libs/socket.io/4.2.0/socket.io.js"></script>
		<script>
			var socket = io();

			document.getElementById('pingForm').onsubmit = function (event) {
				event.preventDefault();
				var domain = document.getElementById('domain').value;
				document.getElementById('pingOutput').innerHTML = '';
				socket.emit('start_ping', { domain: domain });
			};

			socket.on('ping_update', function (data) {
				var pingOutput = document.getElementById('pingOutput');
				pingOutput.innerHTML += data.data + '<br>';
				pingOutput.scrollTop = pingOutput.scrollHeight;
			});
		</script>
	</body>
</html>
